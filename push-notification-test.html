<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Push Notification Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .section h2 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .subscription-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .test-form {
            display: grid;
            gap: 15px;
            margin-top: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        
        input, textarea {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        textarea {
            resize: vertical;
            min-height: 60px;
        }
        
        .server-status {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .server-status.online {
            color: #28a745;
        }
        
        .server-status.offline {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Web Push Notification Test</h1>
        
        <div class="server-status" id="serverStatus">
            <strong>Server Status: </strong><span id="serverStatusText">Checking...</span>
        </div>
        
        <!-- Permission Section -->
        <div class="section">
            <h2>1. Notification Permission</h2>
            <p>First, request permission to show notifications:</p>
            <button id="requestPermissionBtn" onclick="requestNotificationPermission()">
                Request Permission
            </button>
            <div id="permissionStatus" class="status info">
                Permission status: <span id="permissionText">Unknown</span>
            </div>
        </div>
        
        <!-- Subscription Section -->
        <div class="section">
            <h2>2. Push Subscription</h2>
            <p>Subscribe to push notifications from the server:</p>
            <button id="subscribeBtn" onclick="subscribeToPush()" disabled>
                Subscribe to Push
            </button>
            <button id="unsubscribeBtn" onclick="unsubscribeFromPush()" disabled>
                Unsubscribe
            </button>
            <div id="subscriptionStatus" class="status info">
                Subscription status: <span id="subscriptionText">Not subscribed</span>
            </div>
            <div id="subscriptionInfo" class="subscription-info" style="display: none;">
                <strong>Subscription Details:</strong><br>
                <span id="subscriptionDetails"></span>
            </div>
        </div>
        
        <!-- Test Notification Section -->
        <div class="section">
            <h2>3. Send Test Notification</h2>
            <p>Send a test notification through the server:</p>
            
            <div class="test-form">
                <div class="form-group">
                    <label for="notificationTitle">Title:</label>
                    <input type="text" id="notificationTitle" value="Test Notification" />
                </div>
                
                <div class="form-group">
                    <label for="notificationBody">Message:</label>
                    <textarea id="notificationBody">This is a test notification from your web push server!</textarea>
                </div>
                
                <div class="form-group">
                    <label for="notificationUrl">URL (optional):</label>
                    <input type="text" id="notificationUrl" value="/" placeholder="URL to open when clicked" />
                </div>
                
                <div class="form-group">
                    <label for="notificationIcon">Icon URL (optional):</label>
                    <input type="text" id="notificationIcon" value="/favicon.png" placeholder="Icon URL" />
                </div>
            </div>
            
            <button id="sendNotificationBtn" onclick="sendTestNotification()" disabled>
                Send Test Notification
            </button>
            <div id="notificationStatus" class="status info" style="display: none;"></div>
        </div>
        
        <!-- Debug Section -->
        <div class="section">
            <h2>4. Debug Information</h2>
            <button onclick="showDebugInfo()">Show Debug Info</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="debugInfo" class="subscription-info" style="display: none; max-height: 300px;">
                <strong>Debug Logs:</strong><br>
                <div id="debugLogs"></div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const SERVER_URL = 'http://localhost:8000';
        let vapidPublicKey = null;
        let currentSubscription = null;
        let swRegistration = null;
        
        // Debug logging
        const debugLogs = [];
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLogs.push(logEntry);
            console.log(logEntry);
            
            // Update debug display if visible
            const debugLogsDiv = document.getElementById('debugLogs');
            if (debugLogsDiv.parentElement.style.display !== 'none') {
                debugLogsDiv.innerHTML = debugLogs.slice(-50).join('<br>');
                debugLogsDiv.scrollTop = debugLogsDiv.scrollHeight;
            }
        }
        
        // Utility function to convert VAPID key
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');
            
            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);
            
            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }
        
        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch(`${SERVER_URL}/health`);
                if (response.ok) {
                    document.getElementById('serverStatusText').textContent = 'Online ✅';
                    document.getElementById('serverStatus').className = 'server-status online';
                    return true;
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                document.getElementById('serverStatusText').textContent = 'Offline ❌';
                document.getElementById('serverStatus').className = 'server-status offline';
                log(`Server check failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Get VAPID public key from server
        async function getVapidPublicKey() {
            try {
                const response = await fetch(`${SERVER_URL}/api/vapid-public-key`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                vapidPublicKey = data.public_key;
                log(`VAPID public key retrieved: ${vapidPublicKey.substring(0, 20)}...`);
                return vapidPublicKey;
            } catch (error) {
                log(`Failed to get VAPID public key: ${error.message}`, 'error');
                throw error;
            }
        }

        // Initialize the application
        async function init() {
            log('Initializing web push test application...');

            // Check if service workers are supported
            if (!('serviceWorker' in navigator)) {
                log('Service workers not supported', 'error');
                alert('Service workers are not supported in this browser');
                return;
            }

            // Check if push messaging is supported
            if (!('PushManager' in window)) {
                log('Push messaging not supported', 'error');
                alert('Push messaging is not supported in this browser');
                return;
            }

            // Check server status
            const serverOnline = await checkServerStatus();
            if (!serverOnline) {
                log('Server is offline, some features may not work', 'warning');
            }

            // Get VAPID public key
            try {
                await getVapidPublicKey();
            } catch (error) {
                log('Failed to get VAPID key, subscription will not work', 'error');
            }

            // Register service worker
            try {
                swRegistration = await navigator.serviceWorker.register('./sw.js');
                log('Service worker registered successfully');

                // Wait for service worker to be ready
                await navigator.serviceWorker.ready;
                log('Service worker is ready');

            } catch (error) {
                log(`Service worker registration failed: ${error.message}`, 'error');
                alert('Failed to register service worker. Make sure sw.js is in the same directory.');
                return;
            }

            // Check current permission status
            updatePermissionStatus();

            // Check current subscription status
            await updateSubscriptionStatus();

            log('Initialization complete');
        }

        // Update permission status display
        function updatePermissionStatus() {
            const permission = Notification.permission;
            const permissionText = document.getElementById('permissionText');
            const permissionStatus = document.getElementById('permissionStatus');
            const subscribeBtn = document.getElementById('subscribeBtn');

            permissionText.textContent = permission;

            if (permission === 'granted') {
                permissionStatus.className = 'status success';
                subscribeBtn.disabled = false;
                log('Notification permission granted');
            } else if (permission === 'denied') {
                permissionStatus.className = 'status error';
                subscribeBtn.disabled = true;
                log('Notification permission denied');
            } else {
                permissionStatus.className = 'status info';
                subscribeBtn.disabled = true;
                log('Notification permission not yet requested');
            }
        }

        // Request notification permission
        async function requestNotificationPermission() {
            log('Requesting notification permission...');

            try {
                const permission = await Notification.requestPermission();
                log(`Permission result: ${permission}`);
                updatePermissionStatus();

                if (permission === 'granted') {
                    await updateSubscriptionStatus();
                }
            } catch (error) {
                log(`Permission request failed: ${error.message}`, 'error');
            }
        }

        // Update subscription status
        async function updateSubscriptionStatus() {
            if (!swRegistration) {
                log('Service worker not registered', 'error');
                return;
            }

            try {
                currentSubscription = await swRegistration.pushManager.getSubscription();
                const subscriptionText = document.getElementById('subscriptionText');
                const subscriptionStatus = document.getElementById('subscriptionStatus');
                const subscriptionInfo = document.getElementById('subscriptionInfo');
                const subscriptionDetails = document.getElementById('subscriptionDetails');
                const subscribeBtn = document.getElementById('subscribeBtn');
                const unsubscribeBtn = document.getElementById('unsubscribeBtn');
                const sendNotificationBtn = document.getElementById('sendNotificationBtn');

                if (currentSubscription) {
                    subscriptionText.textContent = 'Active';
                    subscriptionStatus.className = 'status success';
                    subscriptionInfo.style.display = 'block';
                    subscriptionDetails.textContent = JSON.stringify(currentSubscription.toJSON(), null, 2);
                    subscribeBtn.disabled = true;
                    unsubscribeBtn.disabled = false;
                    sendNotificationBtn.disabled = false;
                    log('Active subscription found');
                } else {
                    subscriptionText.textContent = 'Not subscribed';
                    subscriptionStatus.className = 'status info';
                    subscriptionInfo.style.display = 'none';
                    subscribeBtn.disabled = Notification.permission !== 'granted';
                    unsubscribeBtn.disabled = true;
                    sendNotificationBtn.disabled = true;
                    log('No active subscription');
                }
            } catch (error) {
                log(`Failed to check subscription status: ${error.message}`, 'error');
            }
        }

        // Subscribe to push notifications
        async function subscribeToPush() {
            if (!swRegistration || !vapidPublicKey) {
                log('Service worker or VAPID key not available', 'error');
                return;
            }

            try {
                log('Subscribing to push notifications...');

                const subscription = await swRegistration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
                });

                log('Push subscription created');

                // Send subscription to server
                const response = await fetch(`${SERVER_URL}/api/subscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        subscription: subscription.toJSON()
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                log(`Subscription sent to server: ${result.message}`);

                await updateSubscriptionStatus();

            } catch (error) {
                log(`Subscription failed: ${error.message}`, 'error');
                alert(`Failed to subscribe: ${error.message}`);
            }
        }

        // Unsubscribe from push notifications
        async function unsubscribeFromPush() {
            if (!currentSubscription) {
                log('No active subscription to unsubscribe from', 'error');
                return;
            }

            try {
                log('Unsubscribing from push notifications...');

                // Unsubscribe from browser
                const successful = await currentSubscription.unsubscribe();

                if (successful) {
                    log('Successfully unsubscribed from browser');

                    // Notify server about unsubscription
                    try {
                        const response = await fetch(`${SERVER_URL}/api/unsubscribe`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                subscription: currentSubscription.toJSON()
                            })
                        });

                        if (response.ok) {
                            log('Server notified about unsubscription');
                        } else {
                            log('Failed to notify server about unsubscription', 'warning');
                        }
                    } catch (error) {
                        log(`Failed to notify server: ${error.message}`, 'warning');
                    }

                    await updateSubscriptionStatus();
                } else {
                    throw new Error('Failed to unsubscribe');
                }

            } catch (error) {
                log(`Unsubscription failed: ${error.message}`, 'error');
                alert(`Failed to unsubscribe: ${error.message}`);
            }
        }

        // Send test notification
        async function sendTestNotification() {
            const title = document.getElementById('notificationTitle').value;
            const body = document.getElementById('notificationBody').value;
            const url = document.getElementById('notificationUrl').value;
            const icon = document.getElementById('notificationIcon').value;
            const notificationStatus = document.getElementById('notificationStatus');

            if (!title || !body) {
                alert('Please enter both title and message');
                return;
            }

            try {
                log('Sending test notification...');
                notificationStatus.style.display = 'block';
                notificationStatus.className = 'status info';
                notificationStatus.textContent = 'Sending notification...';

                const payload = {
                    title: title,
                    body: body,
                    icon: icon || '/favicon.png',
                    url: url || '/',
                    data: { url: url || '/' }
                };

                const response = await fetch(`${SERVER_URL}/api/notifications/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        payload: payload,
                        target_type: 'all'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                log(`Notification sent successfully: ${result.message}`);

                notificationStatus.className = 'status success';
                notificationStatus.textContent = `✅ ${result.message} (Sent: ${result.sent_count}, Failed: ${result.failed_count})`;

            } catch (error) {
                log(`Failed to send notification: ${error.message}`, 'error');
                notificationStatus.className = 'status error';
                notificationStatus.textContent = `❌ Failed to send notification: ${error.message}`;
            }
        }

        // Show debug information
        function showDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const debugLogs = document.getElementById('debugLogs');

            if (debugInfo.style.display === 'none') {
                debugInfo.style.display = 'block';
                debugLogs.innerHTML = debugLogs.slice(-50).join('<br>');
                debugLogs.scrollTop = debugLogs.scrollHeight;
            } else {
                debugInfo.style.display = 'none';
            }
        }

        // Clear debug logs
        function clearLogs() {
            debugLogs.length = 0;
            const debugLogsDiv = document.getElementById('debugLogs');
            debugLogsDiv.innerHTML = '';
            log('Debug logs cleared');
        }

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
