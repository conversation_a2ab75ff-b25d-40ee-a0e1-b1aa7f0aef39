"""Admin panel routes and authentication."""

import secrets
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Request, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPBasicCredentials
from bson import ObjectId

from .config import settings
from .database import db
from .push_service import push_service
from .models import NotificationRequest, NotificationPayload

# Create router
admin_router = APIRouter(prefix="/admin", tags=["admin"])

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Add custom JSON filter for datetime serialization
def datetime_json_filter(obj, **kwargs):
    """Custom JSON filter that handles datetime and ObjectId serialization."""
    def json_serializer(o):
        if isinstance(o, datetime):
            return o.isoformat()
        elif isinstance(o, ObjectId):
            return str(o)
        raise TypeError(f"Object of type {type(o)} is not JSON serializable")

    # Extract indent from kwargs, default to None
    indent = kwargs.get('indent', None)

    return json.dumps(obj, default=json_serializer, indent=indent)

# Register the custom filter
templates.env.filters['tojson'] = datetime_json_filter

# HTTP Basic Auth
security = HTTPBasic()

# Simple session storage (in production, use Redis or database)
active_sessions = {}


def verify_admin_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    """Verify admin credentials."""
    correct_username = secrets.compare_digest(credentials.username, settings.admin_username)
    correct_password = secrets.compare_digest(credentials.password, settings.admin_password)
    
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


@admin_router.get("/", response_class=HTMLResponse)
async def admin_dashboard(request: Request, username: str = Depends(verify_admin_credentials)):
    """Admin dashboard."""
    try:
        # Get statistics
        stats = await db.get_stats()
        
        # Get recent notifications
        recent_notifications = await db.get_recent_notifications(10)
        
        return templates.TemplateResponse(
            "admin_dashboard.html",
            {
                "request": request,
                "username": username,
                "stats": stats,
                "recent_notifications": recent_notifications,
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load dashboard: {str(e)}"
        )


@admin_router.get("/send-notification", response_class=HTMLResponse)
async def send_notification_form(request: Request, username: str = Depends(verify_admin_credentials)):
    """Send notification form."""
    return templates.TemplateResponse(
        "send_notification.html",
        {"request": request, "username": username}
    )


@admin_router.post("/send-notification")
async def send_notification_admin(
    request: Request,
    title: str = Form(...),
    body: str = Form(...),
    url: str = Form("/"),
    icon: str = Form("/favicon.png"),
    target_type: str = Form("all"),
    username: str = Depends(verify_admin_credentials)
):
    """Send notification from admin panel."""
    try:
        # Create notification payload
        payload = NotificationPayload(
            title=title,
            body=body,
            url=url,
            icon=icon,
            data={"url": url}
        )
        
        # Create notification request
        notification_request = NotificationRequest(
            payload=payload,
            target_type=target_type
        )
        
        # Validate VAPID keys
        if not push_service.validate_vapid_keys():
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="VAPID keys not configured"
            )
        
        # Create notification record
        notification_data = notification_request.dict()
        notification_data["created_at"] = datetime.utcnow()
        notification_data["status"] = "pending"
        notification_data["sent_count"] = 0
        notification_data["failed_count"] = 0
        
        notification_id = await db.create_notification(notification_data)
        
        if not notification_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create notification record"
            )
        
        # Send notification
        if target_type == "all":
            result = await push_service.send_notification_to_all(payload)
        elif target_type == "recent":
            result = await push_service.send_notification_to_recent(payload)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid target type"
            )
        
        # Update notification status
        status_value = "sent" if result["sent_count"] > 0 else "failed"
        error_message = None if result["sent_count"] > 0 else "No active subscriptions found"
        
        await db.update_notification_status(
            notification_id,
            status_value,
            result["sent_count"],
            result["failed_count"],
            error_message
        )
        
        # Redirect back to dashboard with success message
        return RedirectResponse(
            url=f"/admin/?success=Notification sent to {result['sent_count']} subscribers",
            status_code=status.HTTP_303_SEE_OTHER
        )
    
    except Exception as e:
        # Redirect back to form with error message
        return RedirectResponse(
            url=f"/admin/send-notification?error={str(e)}",
            status_code=status.HTTP_303_SEE_OTHER
        )


@admin_router.get("/subscriptions", response_class=HTMLResponse)
async def view_subscriptions(request: Request, username: str = Depends(verify_admin_credentials)):
    """View all subscriptions."""
    try:
        # Get all active subscriptions
        subscriptions = await db.get_active_subscriptions()
        
        return templates.TemplateResponse(
            "subscriptions.html",
            {
                "request": request,
                "username": username,
                "subscriptions": subscriptions,
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load subscriptions: {str(e)}"
        )


@admin_router.get("/notifications", response_class=HTMLResponse)
async def view_notifications(request: Request, username: str = Depends(verify_admin_credentials)):
    """View notification history."""
    try:
        # Get recent notifications
        notifications = await db.get_recent_notifications(50)
        
        return templates.TemplateResponse(
            "notifications.html",
            {
                "request": request,
                "username": username,
                "notifications": notifications,
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load notifications: {str(e)}"
        )
