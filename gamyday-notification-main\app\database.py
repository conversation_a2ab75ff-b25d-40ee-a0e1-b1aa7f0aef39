"""Database connection and operations."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import Duplicate<PERSON>eyError
from bson import ObjectId

from .config import settings
from .models import SubscriptionInDB, NotificationInDB, PushSubscription

logger = logging.getLogger(__name__)


class Database:
    """Database manager class."""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
    
    async def connect(self):
        """Connect to MongoDB."""
        try:
            self.client = AsyncIOMotorClient(settings.mongodb_url)
            self.database = self.client[settings.database_name]
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info("Connected to MongoDB successfully")
            
            # Create indexes
            await self.create_indexes()
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    async def create_indexes(self):
        """Create database indexes."""
        try:
            # Subscriptions collection indexes
            await self.database.subscriptions.create_index("subscription.endpoint", unique=True)
            await self.database.subscriptions.create_index("timestamp")
            await self.database.subscriptions.create_index("is_active")
            
            # Notifications collection indexes
            await self.database.notifications.create_index("created_at")
            await self.database.notifications.create_index("status")
            await self.database.notifications.create_index("sent_at")
            
            logger.info("Database indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
    
    # Subscription operations
    async def create_subscription(self, subscription_data: Dict[str, Any]) -> Optional[str]:
        """Create a new subscription."""
        try:
            result = await self.database.subscriptions.insert_one(subscription_data)
            logger.info(f"Created subscription with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except DuplicateKeyError:
            # Update existing subscription
            await self.database.subscriptions.update_one(
                {"subscription.endpoint": subscription_data["subscription"]["endpoint"]},
                {
                    "$set": {
                        "timestamp": subscription_data["timestamp"],
                        "is_active": True,
                        "user_agent": subscription_data.get("user_agent"),
                        "ip_address": subscription_data.get("ip_address"),
                    }
                }
            )
            logger.info("Updated existing subscription")
            return "updated"
        except Exception as e:
            logger.error(f"Failed to create subscription: {e}")
            return None
    
    async def get_subscription_by_endpoint(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """Get subscription by endpoint."""
        try:
            subscription = await self.database.subscriptions.find_one(
                {"subscription.endpoint": endpoint}
            )
            return subscription
        except Exception as e:
            logger.error(f"Failed to get subscription: {e}")
            return None
    
    async def deactivate_subscription(self, endpoint: str) -> bool:
        """Deactivate a subscription."""
        try:
            result = await self.database.subscriptions.update_one(
                {"subscription.endpoint": endpoint},
                {"$set": {"is_active": False}}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to deactivate subscription: {e}")
            return False
    
    async def get_active_subscriptions(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get all active subscriptions."""
        try:
            cursor = self.database.subscriptions.find({"is_active": True})
            if limit:
                cursor = cursor.limit(limit)
            
            subscriptions = await cursor.to_list(length=None)
            return subscriptions
        except Exception as e:
            logger.error(f"Failed to get active subscriptions: {e}")
            return []
    
    async def get_subscriptions_by_ids(self, subscription_ids: List[str]) -> List[Dict[str, Any]]:
        """Get subscriptions by IDs."""
        try:
            object_ids = [ObjectId(id_) for id_ in subscription_ids if ObjectId.is_valid(id_)]
            cursor = self.database.subscriptions.find({
                "_id": {"$in": object_ids},
                "is_active": True
            })
            subscriptions = await cursor.to_list(length=None)
            return subscriptions
        except Exception as e:
            logger.error(f"Failed to get subscriptions by IDs: {e}")
            return []
    
    async def update_subscription_stats(self, endpoint: str):
        """Update subscription notification stats."""
        try:
            await self.database.subscriptions.update_one(
                {"subscription.endpoint": endpoint},
                {
                    "$set": {"last_notification": datetime.utcnow()},
                    "$inc": {"notification_count": 1}
                }
            )
        except Exception as e:
            logger.error(f"Failed to update subscription stats: {e}")
    
    # Notification operations
    async def create_notification(self, notification_data: Dict[str, Any]) -> Optional[str]:
        """Create a new notification record."""
        try:
            result = await self.database.notifications.insert_one(notification_data)
            logger.info(f"Created notification with ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Failed to create notification: {e}")
            return None
    
    async def update_notification_status(
        self, 
        notification_id: str, 
        status: str, 
        sent_count: int = 0, 
        failed_count: int = 0,
        error_message: Optional[str] = None
    ):
        """Update notification status."""
        try:
            update_data = {
                "status": status,
                "sent_count": sent_count,
                "failed_count": failed_count,
            }
            
            if status == "sent":
                update_data["sent_at"] = datetime.utcnow()
            
            if error_message:
                update_data["error_message"] = error_message
            
            await self.database.notifications.update_one(
                {"_id": ObjectId(notification_id)},
                {"$set": update_data}
            )
        except Exception as e:
            logger.error(f"Failed to update notification status: {e}")
    
    async def get_recent_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent notifications."""
        try:
            cursor = self.database.notifications.find().sort("created_at", -1).limit(limit)
            notifications = await cursor.to_list(length=None)

            # Convert to NotificationInDB models to handle proper structure
            from .models import NotificationInDB
            converted_notifications = []
            for notification in notifications:
                try:
                    # Convert to Pydantic model for validation and structure
                    notification_model = NotificationInDB(**notification)
                    # Convert back to dict but keep datetime objects for template compatibility
                    notification_dict = notification_model.model_dump()
                    # Ensure ObjectId is converted to string for JSON compatibility when needed
                    if '_id' in notification_dict:
                        notification_dict['_id'] = str(notification_dict['_id'])
                    converted_notifications.append(notification_dict)
                except Exception as e:
                    logger.warning(f"Failed to convert notification {notification.get('_id')}: {e}")
                    # Fallback: use original notification but ensure ObjectId is string
                    converted_notification = notification.copy()
                    if '_id' in converted_notification:
                        converted_notification['_id'] = str(converted_notification['_id'])
                    converted_notifications.append(converted_notification)

            return converted_notifications
        except Exception as e:
            logger.error(f"Failed to get recent notifications: {e}")
            return []
    
    # Statistics operations
    async def get_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            total_subscriptions = await self.database.subscriptions.count_documents({})
            active_subscriptions = await self.database.subscriptions.count_documents({"is_active": True})
            total_notifications = await self.database.notifications.count_documents({})
            
            # Notifications sent today
            today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            notifications_today = await self.database.notifications.count_documents({
                "sent_at": {"$gte": today},
                "status": "sent"
            })
            
            # Recent notifications
            recent_notifications = await self.get_recent_notifications(5)
            
            return {
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "total_notifications": total_notifications,
                "notifications_sent_today": notifications_today,
                "recent_notifications": recent_notifications,
            }
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {
                "total_subscriptions": 0,
                "active_subscriptions": 0,
                "total_notifications": 0,
                "notifications_sent_today": 0,
                "recent_notifications": [],
            }


# Global database instance
db = Database()
