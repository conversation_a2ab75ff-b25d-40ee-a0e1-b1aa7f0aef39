"""Web push notification service."""

import json
import logging
from typing import List, Dict, Any, Optional
from pywebpush import webpush, WebPushException

from .config import settings
from .database import db
from .models import NotificationPayload, PushSubscription

logger = logging.getLogger(__name__)


class PushNotificationService:
    """Service for sending push notifications."""
    
    def __init__(self):
        self.vapid_private_key = settings.vapid_private_key
        self.vapid_public_key = settings.vapid_public_key
        self.vapid_subject = settings.vapid_subject
    
    async def send_notification_to_subscription(
        self, 
        subscription: Dict[str, Any], 
        payload: NotificationPayload
    ) -> bool:
        """Send notification to a single subscription."""
        try:
            # Prepare the subscription data for pywebpush
            subscription_info = {
                "endpoint": subscription["subscription"]["endpoint"],
                "keys": {
                    "p256dh": subscription["subscription"]["keys"]["p256dh"],
                    "auth": subscription["subscription"]["keys"]["auth"]
                }
            }
            
            # Prepare the payload
            notification_data = {
                "title": payload.title,
                "body": payload.body,
                "icon": payload.icon,
                "badge": payload.badge,
                "data": payload.data or {},
                "tag": payload.tag,
                "requireInteraction": payload.require_interaction,
            }
            
            # Add optional fields
            if payload.image:
                notification_data["image"] = payload.image
            
            if payload.actions:
                notification_data["actions"] = [
                    {
                        "action": action.action,
                        "title": action.title,
                        "icon": action.icon
                    }
                    for action in payload.actions
                ]
            
            if payload.url:
                notification_data["data"]["url"] = payload.url
            
            # Send the notification
            webpush(
                subscription_info=subscription_info,
                data=json.dumps(notification_data),
                vapid_private_key=self.vapid_private_key,
                vapid_claims={
                    "sub": self.vapid_subject,
                    "aud": subscription_info["endpoint"]
                }
            )
            
            # Update subscription stats
            await db.update_subscription_stats(subscription["subscription"]["endpoint"])
            
            logger.info(f"Notification sent successfully to {subscription['subscription']['endpoint']}")
            return True
            
        except WebPushException as e:
            logger.error(f"WebPush error: {e}")
            
            # Handle specific error cases
            if e.response and e.response.status_code in [410, 413, 429]:
                # Subscription is no longer valid, deactivate it
                await db.deactivate_subscription(subscription["subscription"]["endpoint"])
                logger.info(f"Deactivated invalid subscription: {subscription['subscription']['endpoint']}")
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return False
    
    async def send_notification_to_all(self, payload: NotificationPayload) -> Dict[str, int]:
        """Send notification to all active subscriptions."""
        subscriptions = await db.get_active_subscriptions()
        return await self._send_to_subscriptions(subscriptions, payload)
    
    async def send_notification_to_specific(
        self, 
        subscription_ids: List[str], 
        payload: NotificationPayload
    ) -> Dict[str, int]:
        """Send notification to specific subscriptions."""
        subscriptions = await db.get_subscriptions_by_ids(subscription_ids)
        return await self._send_to_subscriptions(subscriptions, payload)
    
    async def send_notification_to_recent(
        self, 
        payload: NotificationPayload, 
        days: int = 7
    ) -> Dict[str, int]:
        """Send notification to recently active subscriptions."""
        # For now, just send to all active subscriptions
        # In the future, you could filter by last_notification date
        subscriptions = await db.get_active_subscriptions()
        return await self._send_to_subscriptions(subscriptions, payload)
    
    async def _send_to_subscriptions(
        self, 
        subscriptions: List[Dict[str, Any]], 
        payload: NotificationPayload
    ) -> Dict[str, int]:
        """Send notification to a list of subscriptions."""
        sent_count = 0
        failed_count = 0
        
        for subscription in subscriptions:
            success = await self.send_notification_to_subscription(subscription, payload)
            if success:
                sent_count += 1
            else:
                failed_count += 1
        
        logger.info(f"Notification batch complete: {sent_count} sent, {failed_count} failed")
        
        return {
            "sent_count": sent_count,
            "failed_count": failed_count,
            "total_count": len(subscriptions)
        }
    
    def validate_vapid_keys(self) -> bool:
        """Validate that VAPID keys are configured."""
        return bool(self.vapid_private_key and self.vapid_public_key and self.vapid_subject)


# Global push service instance
push_service = PushNotificationService()
